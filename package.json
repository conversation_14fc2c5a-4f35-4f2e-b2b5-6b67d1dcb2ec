{"name": "bun-react-demo", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "bun run react-scripts start", "build": "bun run react-scripts build", "test": "bun run react-scripts test", "eject": "bun run react-scripts eject", "dev": "bun run start", "start:npm": "react-scripts start", "build:npm": "react-scripts build", "test:npm": "react-scripts test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}